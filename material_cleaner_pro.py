bl_info = {
    "name": "Material Cleaner Pro",
    "author": "Your Name",
    "version": (1, 3),
    "blender": (3, 6, 0),
    "location": "3D View > N-Panel > Material Cleaner Pro",
    "description": (
        "Removes duplicate materials with identical node setups, cleans unused material slots, "
        "and provides both single-click and step-by-step options with chunk processing."
    ),
    "category": "Object",
}

import bpy
import re


# ----------------------------------------------------------------
# Property Group to Store State
# ----------------------------------------------------------------
class MaterialCleanerProProps(bpy.types.PropertyGroup):
    """
    Stores the state and messages for the Material Cleaner Pro add-on.
    """
    analysis_done: bpy.props.BoolProperty(name="Analysis Done", default=False)
    analysis_message: bpy.props.StringProperty(name="Analysis Message", default="")
    merges_done: bpy.props.BoolProperty(name="Merges Done", default=False)
    merges_message: bpy.props.StringProperty(name="Merges Message", default="")
    cleaning_done: bpy.props.BoolProperty(name="Cleaning Done", default=False)
    cleaning_message: bpy.props.StringProperty(name="Cleaning Message", default="")
    material_list: bpy.props.CollectionProperty(type=bpy.types.PropertyGroup)
    current_index: bpy.props.IntProperty(name="Current Index", default=0)
    duplicates_removed_total: bpy.props.IntProperty(name="Duplicates Removed Total", default=0)
    chunk_size: bpy.props.IntProperty(
        name="Chunk Size",
        default=50,
        min=1,
        description="Number of materials to process per chunk"
    )


# ----------------------------------------------------------------
# Utility Functions
# ----------------------------------------------------------------
def popup_message(context, title, message, icon='INFO'):
    """Displays a popup dialog."""
    def draw(self, _context):
        for line in message.split("\n"):
            self.layout.label(text=line)
    context.window_manager.popup_menu(draw, title=title, icon=icon)


def remove_all_materials_from_objects(context):
    """Removes all materials from the selected object(s)."""
    selected_objects = context.selected_objects
    if not selected_objects:
        return 0

    removed_count = 0
    for obj in selected_objects:
        if hasattr(obj.data, "materials"):
            obj.data.materials.clear()
            removed_count += 1

    return removed_count


def clean_materials_from_objects(context):
    """Cleans materials for the selected object(s)."""
    selected_objects = context.selected_objects
    if not selected_objects:
        return 0

    props = context.scene.material_cleanerpro_props
    total_cleaned = 0

    for obj in selected_objects:
        if hasattr(obj.data, "materials"):
            total_cleaned += remove_unused_material_slots_manual(props)

    total_cleaned += smart_merge_duplicate_materials(props)

    return total_cleaned


# ----------------------------------------------------------------
# New Operators
# ----------------------------------------------------------------
class MATERIALCLEANERPRO_OT_remove_all_materials(bpy.types.Operator):
    """Remove all materials from the selected object(s)."""
    bl_idname = "materialcleanerpro.remove_all_materials"
    bl_label = "Remove All Materials"

    def execute(self, context):
        removed = remove_all_materials_from_objects(context)
        if removed > 0:
            self.report({'INFO'}, f"Removed materials from {removed} object(s).")
        else:
            self.report({'WARNING'}, "No materials removed. Select objects first.")
        return {'FINISHED'}


class MATERIALCLEANERPRO_OT_clean_materials(bpy.types.Operator):
    """Clean materials: Remove unused slots and merge duplicates."""
    bl_idname = "materialcleanerpro.clean_materials"
    bl_label = "Clean Materials"

    def execute(self, context):
        cleaned = clean_materials_from_objects(context)
        if cleaned > 0:
            self.report({'INFO'}, f"Cleaned materials for selected object(s).")
        else:
            self.report({'WARNING'}, "No materials cleaned. Select objects first.")
        return {'FINISHED'}


# ----------------------------------------------------------------
# Context Menu Integration
# ----------------------------------------------------------------
def context_menu(self, context):
    layout = self.layout
    if context.object:  # Ensure an object is selected
        layout.separator()  # Adds a dividing line
        layout.label(text="Material Cleaner Pro", icon='MATERIAL')
        layout.operator("materialcleanerpro.remove_all_materials", text="Remove All Materials")
        layout.operator("materialcleanerpro.clean_materials", text="Clean Materials")


# ----------------------------------------------------------------
# N-Panel Integration
# ----------------------------------------------------------------
class MATERIALCLEANERPRO_PT_panel(bpy.types.Panel):
    """Creates a panel in the 3D View N-Panel for Material Cleaner Pro"""
    bl_label = "Material Cleaner Pro"
    bl_idname = "MATERIALCLEANERPRO_PT_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "Material Cleaner Pro"

    def draw(self, context):
        layout = self.layout
        props = context.scene.material_cleanerpro_props

        # Existing Buttons
        layout.operator("materialcleanerpro.cleanse_all", text="Cleanse All")
        layout.separator()

        layout.label(text="Step-by-Step Cleaning:", icon='INFO')
        layout.operator("materialcleanerpro.step_analyze", text="1. Analyze Materials")
        layout.operator("materialcleanerpro.step_merge", text="2. Merge Duplicates")
        layout.operator("materialcleanerpro.step_clean", text="3. Clean Unused Slots")
        layout.separator()

        # New Buttons
        layout.label(text="Object-Level Operations:", icon='OBJECT_DATA')
        layout.operator("materialcleanerpro.remove_all_materials", text="Remove All Materials")
        layout.operator("materialcleanerpro.clean_materials", text="Clean Materials")


# ----------------------------------------------------------------
# Registration
# ----------------------------------------------------------------
classes = (
    MaterialCleanerProProps,
    MATERIALCLEANERPRO_OT_remove_all_materials,
    MATERIALCLEANERPRO_OT_clean_materials,
    MATERIALCLEANERPRO_PT_panel,
)

def register():
    for cls in classes:
        bpy.utils.register_class(cls)
    bpy.types.Scene.material_cleanerpro_props = bpy.props.PointerProperty(type=MaterialCleanerProProps)
    bpy.types.VIEW3D_MT_object.append(context_menu)  # Add to the Object menu
    bpy.types.VIEW3D_MT_context_menu.append(context_menu)  # Add to the Right-Click menu


def unregister():
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
    del bpy.types.Scene.material_cleanerpro_props
    bpy.types.VIEW3D_MT_object.remove(context_menu)
    bpy.types.VIEW3D_MT_context_menu.remove(context_menu)


if __name__ == "__main__":
    register()
