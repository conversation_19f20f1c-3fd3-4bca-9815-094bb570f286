# Material Cleaner Pro - Installation Guide

## Installation Methods

### Method 1: Single File Installation (Recommended)
This method installs the add-on as a single Python file, which is the most compatible approach.

1. **Prepare the single file**:
   - Copy all the code from `__init__.py` 
   - Save it as `material_cleaner_pro_single.py`

2. **Install in Blender**:
   - Open Blender
   - Go to `Edit > Preferences > Add-ons`
   - Click `Install...`
   - Select the `material_cleaner_pro_single.py` file
   - Enable "Material Cleaner Pro" in the add-ons list

### Method 2: Package Installation
This method installs the add-on as a package with multiple files.

1. **Create the package structure**:
   ```
   material_cleaner_pro/
   ├── __init__.py
   ├── operators.py
   ├── panel.py
   ├── properties.py
   └── utils.py
   ```

2. **Install in Blender**:
   - Zip the `material_cleaner_pro` folder
   - In Blender: `Edit > Preferences > Add-ons`
   - Click `Install...`
   - Select the zip file
   - Enable "Material Cleaner Pro"

### Method 3: Manual Installation
Copy files directly to Blender's add-ons directory.

**Windows**: `%APPDATA%\Blender Foundation\Blender\[version]\scripts\addons\`
**macOS**: `~/Library/Application Support/Blender/[version]/scripts/addons/`
**Linux**: `~/.config/blender/[version]/scripts/addons/`

## Troubleshooting Installation Issues

### Error: "attempted relative import with no known parent package"
**Solution**: Use Method 1 (Single File Installation)

### Error: "ZIP packaged incorrectly"
**Solution**: Ensure the zip contains a folder, not loose files:
```
material_cleaner_pro.zip
└── material_cleaner_pro/
    ├── __init__.py
    ├── operators.py
    └── ...
```

### Add-on doesn't appear in preferences
1. Check if the file has a `.py` extension
2. Verify the `bl_info` dictionary is present
3. Restart Blender after installation

### Import errors
1. Ensure all required files are present
2. Check file permissions
3. Try the single file installation method

## Verification

After installation:
1. Enable the add-on in preferences
2. Open 3D Viewport
3. Press `N` to open the N-Panel
4. Look for "Material Cleaner Pro" tab
5. Test with "Cleanse All" button

## Support

If you encounter issues:
1. Check Blender's console for error messages
2. Try the single file installation method
3. Ensure you're using Blender 3.6 or newer
4. Verify all files are present and readable

---

**Note**: The single file installation method is most reliable and avoids import issues.
