# Material Cleaner Pro

**Advanced Material Management Tool for Blender**

Material Cleaner Pro is a comprehensive Blender add-on designed to streamline material management in complex projects. It provides intelligent duplicate detection, unused slot cleanup, and performance optimization tools.

## Features

### 🚀 Core Functionality
- **Smart Duplicate Detection**: Identifies materials with identical node setups, not just names
- **Intelligent Merging**: Safely merges duplicate materials while preserving references
- **Unused Slot Cleanup**: Removes empty and unused material slots from objects
- **Orphaned Material Removal**: Cleans up materials with no users

### 🔧 Advanced Tools
- **Chunk Processing**: Handles large projects with 500+ materials efficiently
- **Progress Tracking**: Real-time progress bars and status updates
- **Material Validation**: Checks for corrupted or problematic materials
- **Performance Optimization**: Removes unused nodes and optimizes material trees
- **Batch Operations**: Process selected objects or entire scenes

### 🎯 User Experience
- **One-Click Solution**: "Cleanse All" for quick cleanup
- **Step-by-Step Workflow**: Guided process with visual feedback
- **Multiple Access Points**: N-Panel, context menus, and operators
- **Comprehensive Logging**: Detailed logs for troubleshooting

## Installation

### Method 1: Direct Installation
1. Download the entire `scripts` folder
2. In Blender, go to `Edit > Preferences > Add-ons`
3. Click `Install...` and select the `scripts` folder
4. Enable "Material Cleaner Pro" in the add-ons list

### Method 2: Manual Installation
1. Copy the `scripts` folder to your Blender add-ons directory:
   - **Windows**: `%APPDATA%\Blender Foundation\Blender\[version]\scripts\addons\`
   - **macOS**: `~/Library/Application Support/Blender/[version]/scripts/addons/`
   - **Linux**: `~/.config/blender/[version]/scripts/addons/`
2. Restart Blender
3. Enable the add-on in Preferences

## Usage

### Quick Start
1. Open the **N-Panel** in the 3D Viewport (press `N`)
2. Find the **"Material Cleaner Pro"** tab
3. Click **"Cleanse All"** for automatic cleanup

### Step-by-Step Process
1. **Analyze Materials**: Scan and count materials in your scene
2. **Merge Duplicates**: Intelligently merge materials with identical setups
3. **Clean Unused Slots**: Remove empty and unused material slots
4. **Done**: Reset for the next cleanup cycle

### Advanced Operations
- **Validate Materials**: Check for corrupted or problematic materials
- **Chunk Process**: Use for projects with 500+ materials
- **Object-Level Operations**: Work with selected objects only

### Context Menu Access
- Right-click in the 3D Viewport
- Go to `Object > Material Cleaner Pro` menu
- Choose from available operations

## Configuration

### Settings
- **Chunk Size**: Number of materials to process per batch (default: 50)
- Adjust based on your system's performance and project size

### Logging
- Logs are saved to Blender's temporary directory
- Check `material_cleaner_pro.log` for detailed operation reports

## Technical Details

### Algorithm Features
- **Node Signature Comparison**: Compares complete node tree structures
- **Safe Material Replacement**: Uses Blender's `user_remap()` for safe references
- **Error Recovery**: Comprehensive error handling and validation
- **Memory Optimization**: Efficient processing for large material counts

### Performance
- **Chunk Processing**: Handles large projects without freezing Blender
- **Progress Tracking**: Real-time updates during long operations
- **Memory Management**: Optimized for minimal memory usage

## Troubleshooting

### Common Issues
1. **Add-on not appearing**: Check if it's enabled in Preferences
2. **No materials found**: Ensure your scene has materials to process
3. **Slow performance**: Use chunk processing for large projects
4. **Unexpected results**: Check the log file for detailed information

### Performance Tips
- Use "Chunk Process" for projects with 500+ materials
- Run "Validate Materials" before major cleanup operations
- Save your project before running cleanup operations

## Development

### File Structure
```
scripts/
├── __init__.py          # Package initialization
├── material_cleaner_pro.py  # Main entry point
├── operators.py         # Blender operators
├── panel.py            # UI panels
├── properties.py       # Property definitions
├── utils.py            # Core algorithms
└── test_material_cleaner.py  # Test suite
```

### Testing
Run the test suite in Blender's scripting workspace:
```python
exec(open("test_material_cleaner.py").read())
```

## Version History

### v1.4.0 (Current)
- Added chunk processing for large projects
- Improved error handling and validation
- Enhanced progress tracking and UI feedback
- Added material validation and optimization tools
- Comprehensive code cleanup and optimization

### v1.3.0
- Initial release with core functionality
- Basic duplicate detection and cleanup
- Step-by-step workflow implementation

## License

This project is open source. Feel free to modify and distribute according to your needs.

## Support

For issues, suggestions, or contributions:
1. Check the log file for detailed error information
2. Use the "Validate Materials" function to identify problems
3. Test with the included test suite

---

**Material Cleaner Pro** - Making Blender material management effortless! 🎨
