#!/usr/bin/env python3
"""
Validation script for Material Cleaner Pro add-on
Checks file structure, imports, and basic functionality
"""

import os
import sys
import ast
import importlib.util

def check_file_exists(filepath):
    """Check if a file exists and return its status."""
    if os.path.exists(filepath):
        size = os.path.getsize(filepath)
        return True, f"✅ {filepath} ({size} bytes)"
    else:
        return False, f"❌ {filepath} - FILE MISSING"

def check_python_syntax(filepath):
    """Check if a Python file has valid syntax."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        ast.parse(content)
        return True, f"✅ {filepath} - Valid Python syntax"
    except SyntaxError as e:
        return False, f"❌ {filepath} - Syntax Error: {e}"
    except Exception as e:
        return False, f"❌ {filepath} - Error: {e}"

def check_bl_info(filepath):
    """Check if bl_info is properly defined."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'bl_info' in content:
            # Try to extract bl_info
            tree = ast.parse(content)
            for node in ast.walk(tree):
                if isinstance(node, ast.Assign):
                    for target in node.targets:
                        if isinstance(target, ast.Name) and target.id == 'bl_info':
                            return True, f"✅ {filepath} - bl_info found"
            return False, f"⚠️  {filepath} - bl_info mentioned but not properly defined"
        else:
            return False, f"⚠️  {filepath} - No bl_info found"
    except Exception as e:
        return False, f"❌ {filepath} - Error checking bl_info: {e}"

def check_registration_functions(filepath):
    """Check if register/unregister functions exist."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        has_register = 'def register(' in content
        has_unregister = 'def unregister(' in content
        
        if has_register and has_unregister:
            return True, f"✅ {filepath} - Both register/unregister functions found"
        elif has_register:
            return False, f"⚠️  {filepath} - Only register function found"
        elif has_unregister:
            return False, f"⚠️  {filepath} - Only unregister function found"
        else:
            return False, f"⚠️  {filepath} - No registration functions found"
    except Exception as e:
        return False, f"❌ {filepath} - Error checking registration: {e}"

def validate_addon_structure():
    """Validate the complete add-on structure."""
    print("Material Cleaner Pro - Add-on Validation")
    print("=" * 50)
    
    # Define expected files
    files_to_check = [
        '__init__.py',
        'material_cleaner_pro.py',
        'operators.py',
        'panel.py',
        'properties.py',
        'utils.py'
    ]
    
    optional_files = [
        'README.md',
        'test_material_cleaner.py',
        'validate_addon.py'
    ]
    
    all_good = True
    
    print("\n1. Checking file existence:")
    print("-" * 30)
    for filepath in files_to_check:
        exists, message = check_file_exists(filepath)
        print(message)
        if not exists:
            all_good = False
    
    print("\n   Optional files:")
    for filepath in optional_files:
        exists, message = check_file_exists(filepath)
        print(f"   {message}")
    
    print("\n2. Checking Python syntax:")
    print("-" * 30)
    for filepath in files_to_check:
        if os.path.exists(filepath):
            valid, message = check_python_syntax(filepath)
            print(message)
            if not valid:
                all_good = False
    
    print("\n3. Checking bl_info:")
    print("-" * 30)
    main_files = ['__init__.py', 'material_cleaner_pro.py']
    for filepath in main_files:
        if os.path.exists(filepath):
            valid, message = check_bl_info(filepath)
            print(message)
    
    print("\n4. Checking registration functions:")
    print("-" * 30)
    reg_files = ['__init__.py', 'material_cleaner_pro.py', 'operators.py', 'panel.py', 'properties.py']
    for filepath in reg_files:
        if os.path.exists(filepath):
            valid, message = check_registration_functions(filepath)
            print(message)
    
    print("\n5. Checking class definitions:")
    print("-" * 30)
    
    # Check for expected classes
    expected_classes = {
        'properties.py': ['MaterialCleanerProProps'],
        'operators.py': ['MATERIALCLEANERPRO_OT_cleanse_all', 'MATERIALCLEANERPRO_OT_step_analyze'],
        'panel.py': ['MATERIALCLEANERPRO_PT_panel'],
    }
    
    for filepath, classes in expected_classes.items():
        if os.path.exists(filepath):
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                found_classes = []
                for class_name in classes:
                    if f'class {class_name}' in content:
                        found_classes.append(class_name)
                
                if found_classes:
                    print(f"✅ {filepath} - Found classes: {', '.join(found_classes)}")
                else:
                    print(f"⚠️  {filepath} - No expected classes found")
            except Exception as e:
                print(f"❌ {filepath} - Error checking classes: {e}")
    
    print("\n6. Summary:")
    print("-" * 30)
    if all_good:
        print("🎉 Add-on structure validation PASSED!")
        print("   The add-on appears to be properly structured.")
    else:
        print("⚠️  Add-on structure validation found issues.")
        print("   Please review the errors above.")
    
    print("\n7. Installation Notes:")
    print("-" * 30)
    print("• Ensure all files are in the same directory")
    print("• Install the entire folder as a Blender add-on")
    print("• Enable 'Material Cleaner Pro' in Blender preferences")
    print("• Check the N-Panel in 3D Viewport for the interface")
    
    return all_good

if __name__ == "__main__":
    validate_addon_structure()
