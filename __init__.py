bl_info = {
    "name": "Material Cleaner Pro",
    "author": "Material Cleaner Pro Team",
    "version": (1, 4, 0),
    "blender": (3, 6, 0),
    "location": "3D View > N-Panel > Material Cleaner Pro",
    "description": (
        "Advanced material management tool that removes duplicate materials with identical node setups, "
        "cleans unused material slots, and provides both single-click and step-by-step options with "
        "chunk processing for large projects."
    ),
    "category": "Material",
    "doc_url": "",
    "tracker_url": "",
}

import bpy

# Check if we're in a package or standalone
try:
    from . import operators, panel, properties, utils
    PACKAGE_MODE = True
except ImportError:
    # Standalone mode - import from same directory
    import operators, panel, properties, utils
    PACKAGE_MODE = False

# ----------------------------------------------------------------
# Context Menu Integration
# ----------------------------------------------------------------
def context_menu(self, context):
    """Add Material Cleaner Pro options to context menus."""
    layout = self.layout
    if context.object:  # Ensure an object is selected
        layout.separator()  # Adds a dividing line
        layout.label(text="Material Cleaner Pro", icon='MATERIAL')
        layout.operator("materialcleanerpro.remove_all_materials", text="Remove All Materials")
        layout.operator("materialcleanerpro.clean_materials", text="Clean Materials")


# ----------------------------------------------------------------
# Registration
# ----------------------------------------------------------------
def register():
    """Register all classes and properties."""
    # Register all modules
    properties.register()
    operators.register()
    panel.register()

    # Register context menu
    bpy.types.VIEW3D_MT_object.append(context_menu)
    bpy.types.VIEW3D_MT_context_menu.append(context_menu)


def unregister():
    """Unregister all classes and properties."""
    # Remove context menu
    try:
        bpy.types.VIEW3D_MT_object.remove(context_menu)
        bpy.types.VIEW3D_MT_context_menu.remove(context_menu)
    except ValueError:
        pass  # Already removed

    # Unregister all modules
    panel.unregister()
    operators.unregister()
    properties.unregister()


if __name__ == "__main__":
    register()