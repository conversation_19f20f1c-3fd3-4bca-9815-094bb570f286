# Material Cleaner Pro - Blender Add-on
# Advanced material management tool for Blender

# Import the main module to make it available when the package is imported
from . import material_cleaner_pro

# Re-export the bl_info for Blender to recognize this as an add-on
bl_info = material_cleaner_pro.bl_info

# Re-export registration functions
register = material_cleaner_pro.register
unregister = material_cleaner_pro.unregister