# scripts/properties.py

import bpy
import re

class MaterialCleanerProProps(bpy.types.PropertyGroup):
    """
    Stores the state and messages for the Material Cleaner Pro add-on.
    """
    # Analysis
    analysis_done: bpy.props.BoolProperty(name="Analysis Done", default=False)
    analysis_message: bpy.props.StringProperty(name="Analysis Message", default="")
    
    # Merging
    merges_done: bpy.props.BoolProperty(name="Merges Done", default=False)
    merges_message: bpy.props.StringProperty(name="Merges Message", default="")
    
    # Cleaning
    cleaning_done: bpy.props.BoolProperty(name="Cleaning Done", default=False)
    cleaning_message: bpy.props.StringProperty(name="Cleaning Message", default="")
    
    # Chunking for Stepwise
    material_list: bpy.props.CollectionProperty(type=bpy.types.PropertyGroup)
    current_index: bpy.props.IntProperty(name="Current Index", default=0)
    duplicates_removed_total: bpy.props.IntProperty(name="Duplicates Removed Total", default=0)
    chunk_size: bpy.props.IntProperty(
        name="Chunk Size",
        default=50,
        min=1,
        description="Number of materials to process per chunk"
    )

    # Progress tracking
    progress_current: bpy.props.IntProperty(name="Progress Current", default=0)
    progress_total: bpy.props.IntProperty(name="Progress Total", default=0)
    progress_message: bpy.props.StringProperty(name="Progress Message", default="")


# ----------------------------------------------------------------
# Registration
# ----------------------------------------------------------------
classes = (
    MaterialCleanerProProps,
)


def register():
    """Register all property classes."""
    for cls in classes:
        bpy.utils.register_class(cls)
    bpy.types.Scene.material_cleanerpro_props = bpy.props.PointerProperty(type=MaterialCleanerProProps)


def unregister():
    """Unregister all property classes."""
    if hasattr(bpy.types.Scene, 'material_cleanerpro_props'):
        del bpy.types.Scene.material_cleanerpro_props
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
