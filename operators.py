# scripts/operators.py

import bpy
from .utils import popup_message, smart_merge_duplicate_materials, remove_unused_material_slots_manual
import logging

class MATERIALCLEANERPRO_OT_cleanse_all(bpy.types.Operator):
    """Performs all steps: Analyze, Merge, Clean with optional chunk processing"""
    bl_idname = "materialcleanerpro.cleanse_all"
    bl_label = "Cleanse All"
    bl_options = {'REGISTER', 'UNDO'}

    use_chunking: bpy.props.BoolProperty(
        name="Use Chunk Processing",
        description="Use chunk processing for large material counts",
        default=False
    )

    def execute(self, context):
        props = context.scene.material_cleanerpro_props
        logging.info("Starting 'Cleanse All' operation.")

        # Determine if we should use chunking based on material count
        num_mats = len(bpy.data.materials)
        use_chunking = self.use_chunking or num_mats > props.chunk_size * 2

        # Step 1: Analyze
        props.analysis_message = f"Found {num_mats} materials."
        props.analysis_done = True
        logging.info(f"Analysis: {props.analysis_message}")

        # Step 2: Merge Duplicates
        merged = smart_merge_duplicate_materials(props, use_chunking=use_chunking)
        props.merges_message = f"Merged {merged} duplicate material(s)."
        props.merges_done = True
        logging.info(f"Merges: {props.merges_message}")

        # Step 3: Clean Unused Slots
        cleaned = remove_unused_material_slots_manual(props, use_chunking=use_chunking)
        props.cleaning_message = f"Removed {cleaned} unused material slot(s)."
        props.cleaning_done = True
        logging.info(f"Cleaning: {props.cleaning_message}")

        # Step 4: Clean orphaned materials
        from .utils import cleanup_orphaned_materials
        orphaned = cleanup_orphaned_materials()
        if orphaned > 0:
            props.cleaning_message += f" Removed {orphaned} orphaned material(s)."

        # Feedback
        popup_message(
            context,
            "Cleanse All Completed",
            f"Analysis: {props.analysis_message}\n"
            f"Merges: {props.merges_message}\n"
            f"Cleaning: {props.cleaning_message}",
            'INFO'
        )

        self.report({'INFO'}, "Cleanse All Completed.")
        logging.info("'Cleanse All' operation completed successfully.")
        return {'FINISHED'}

class MATERIALCLEANERPRO_OT_step_analyze(bpy.types.Operator):
    """Step 1: Analyze Materials"""
    bl_idname = "materialcleanerpro.step_analyze"
    bl_label = "1. Analyze Materials"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        props = context.scene.material_cleanerpro_props
        num_mats = len(bpy.data.materials)
        props.analysis_message = f"Found {num_mats} materials."
        props.analysis_done = True
        logging.info(f"Step 1 - Analyze: {props.analysis_message}")

        self.report({'INFO'}, "Analysis Completed.")
        return {'FINISHED'}

class MATERIALCLEANERPRO_OT_step_merge(bpy.types.Operator):
    """Step 2: Merge Duplicate Materials"""
    bl_idname = "materialcleanerpro.step_merge"
    bl_label = "2. Merge Duplicates"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        props = context.scene.material_cleanerpro_props
        if not props.analysis_done:
            self.report({'WARNING'}, "Please complete Step 1: Analyze first.")
            logging.warning("Attempted to merge duplicates without completing analysis.")
            return {'CANCELLED'}

        # Use chunking for large material counts
        num_mats = len(bpy.data.materials)
        use_chunking = num_mats > props.chunk_size

        merged = smart_merge_duplicate_materials(props, use_chunking=use_chunking)
        props.merges_message = f"Merged {merged} duplicate material(s)."
        props.merges_done = True
        logging.info(f"Step 2 - Merge: {props.merges_message}")

        self.report({'INFO'}, f"Merged {merged} duplicate material(s).")
        return {'FINISHED'}

class MATERIALCLEANERPRO_OT_step_clean(bpy.types.Operator):
    """Step 3: Clean Unused Material Slots"""
    bl_idname = "materialcleanerpro.step_clean"
    bl_label = "3. Clean Unused Slots"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        props = context.scene.material_cleanerpro_props
        if not props.merges_done:
            self.report({'WARNING'}, "Please complete Step 2: Merge first.")
            logging.warning("Attempted to clean unused slots without completing merges.")
            return {'CANCELLED'}

        # Use chunking for large object counts
        num_objects = len([obj for obj in bpy.data.objects
                          if hasattr(obj, "data") and hasattr(obj.data, "materials")])
        use_chunking = num_objects > props.chunk_size

        cleaned = remove_unused_material_slots_manual(props, use_chunking=use_chunking)

        # Also clean orphaned materials
        from .utils import cleanup_orphaned_materials
        orphaned = cleanup_orphaned_materials()

        total_cleaned = cleaned + orphaned
        props.cleaning_message = f"Removed {cleaned} unused slot(s) and {orphaned} orphaned material(s)."
        props.cleaning_done = True
        logging.info(f"Step 3 - Clean: {props.cleaning_message}")

        self.report({'INFO'}, f"Removed {total_cleaned} items total.")
        return {'FINISHED'}

class MATERIALCLEANERPRO_OT_done(bpy.types.Operator):
    """Final Confirmation - Marks the process as done"""
    bl_idname = "materialcleanerpro.done"
    bl_label = "Done"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        props = context.scene.material_cleanerpro_props

        # Reset all steps for future runs
        props.analysis_done = False
        props.analysis_message = ""
        props.merges_done = False
        props.merges_message = ""
        props.cleaning_done = False
        props.cleaning_message = ""
        props.current_index = 0
        props.duplicates_removed_total = 0

        self.report({'INFO'}, "Process completed and reset.")
        logging.info("Process completed and reset.")

        return {'FINISHED'}


class MATERIALCLEANERPRO_OT_validate_materials(bpy.types.Operator):
    """Validate material integrity and report issues"""
    bl_idname = "materialcleanerpro.validate_materials"
    bl_label = "Validate Materials"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        from .utils import validate_material_integrity

        issues = validate_material_integrity()

        if issues:
            issue_text = "\n".join(issues)
            popup_message(
                context,
                "Material Validation Issues Found",
                f"Found {len(issues)} issues:\n{issue_text}",
                'WARNING'
            )
            self.report({'WARNING'}, f"Found {len(issues)} material issues. Check console for details.")
            logging.warning(f"Material validation issues:\n{issue_text}")
        else:
            popup_message(
                context,
                "Material Validation Complete",
                "No issues found. All materials are valid.",
                'INFO'
            )
            self.report({'INFO'}, "All materials are valid.")

        return {'FINISHED'}


class MATERIALCLEANERPRO_OT_chunk_process(bpy.types.Operator):
    """Process materials in chunks for large projects"""
    bl_idname = "materialcleanerpro.chunk_process"
    bl_label = "Chunk Process Materials"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        props = context.scene.material_cleanerpro_props

        # Force chunking mode
        num_mats = len(bpy.data.materials)
        if num_mats == 0:
            self.report({'INFO'}, "No materials to process.")
            return {'FINISHED'}

        self.report({'INFO'}, f"Processing {num_mats} materials in chunks of {props.chunk_size}...")

        # Step 1: Merge duplicates with chunking
        merged = smart_merge_duplicate_materials(props, use_chunking=True)

        # Step 2: Clean unused slots with chunking
        cleaned = remove_unused_material_slots_manual(props, use_chunking=True)

        # Step 3: Clean orphaned materials
        from .utils import cleanup_orphaned_materials
        orphaned = cleanup_orphaned_materials()

        # Report results
        popup_message(
            context,
            "Chunk Processing Complete",
            f"Merged: {merged} duplicates\n"
            f"Cleaned: {cleaned} unused slots\n"
            f"Removed: {orphaned} orphaned materials",
            'INFO'
        )

        self.report({'INFO'}, f"Chunk processing complete: {merged + cleaned + orphaned} items processed.")
        return {'FINISHED'}


class MATERIALCLEANERPRO_OT_remove_all_materials(bpy.types.Operator):
    """Remove all materials from the selected object(s)."""
    bl_idname = "materialcleanerpro.remove_all_materials"
    bl_label = "Remove All Materials"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        selected_objects = context.selected_objects
        if not selected_objects:
            self.report({'WARNING'}, "No objects selected.")
            return {'CANCELLED'}

        removed_count = 0
        for obj in selected_objects:
            if hasattr(obj, "data") and hasattr(obj.data, "materials"):
                material_count = len(obj.data.materials)
                obj.data.materials.clear()
                if material_count > 0:
                    removed_count += 1
                    logging.info(f"Removed {material_count} materials from object '{obj.name}'.")

        if removed_count > 0:
            self.report({'INFO'}, f"Removed materials from {removed_count} object(s).")
        else:
            self.report({'INFO'}, "No materials to remove from selected objects.")

        return {'FINISHED'}


class MATERIALCLEANERPRO_OT_clean_materials(bpy.types.Operator):
    """Clean materials: Remove unused slots and merge duplicates for selected objects."""
    bl_idname = "materialcleanerpro.clean_materials"
    bl_label = "Clean Materials"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        selected_objects = context.selected_objects
        if not selected_objects:
            self.report({'WARNING'}, "No objects selected.")
            return {'CANCELLED'}

        props = context.scene.material_cleanerpro_props

        # First, merge duplicate materials globally
        merged = smart_merge_duplicate_materials(props)

        # Then, clean unused slots from selected objects
        total_slots_removed = 0
        for obj in selected_objects:
            if hasattr(obj, "data") and hasattr(obj.data, "materials"):
                material_list = obj.data.materials
                slots_removed = 0

                # Iterate in reverse to safely remove slots
                for slot_idx in reversed(range(len(material_list))):
                    mat = material_list[slot_idx]
                    if mat is None or mat.users == 0:
                        material_list.pop(index=slot_idx)
                        slots_removed += 1
                        logging.debug(f"Removed unused material slot at index {slot_idx} from object '{obj.name}'.")

                total_slots_removed += slots_removed

        # Report results
        message_parts = []
        if merged > 0:
            message_parts.append(f"Merged {merged} duplicate material(s)")
        if total_slots_removed > 0:
            message_parts.append(f"Removed {total_slots_removed} unused slot(s)")

        if message_parts:
            self.report({'INFO'}, ". ".join(message_parts) + ".")
        else:
            self.report({'INFO'}, "No materials needed cleaning.")

        return {'FINISHED'}


# ----------------------------------------------------------------
# Registration
# ----------------------------------------------------------------
classes = (
    MATERIALCLEANERPRO_OT_cleanse_all,
    MATERIALCLEANERPRO_OT_step_analyze,
    MATERIALCLEANERPRO_OT_step_merge,
    MATERIALCLEANERPRO_OT_step_clean,
    MATERIALCLEANERPRO_OT_done,
    MATERIALCLEANERPRO_OT_validate_materials,
    MATERIALCLEANERPRO_OT_chunk_process,
    MATERIALCLEANERPRO_OT_remove_all_materials,
    MATERIALCLEANERPRO_OT_clean_materials,
)


def register():
    """Register all operator classes."""
    for cls in classes:
        bpy.utils.register_class(cls)


def unregister():
    """Unregister all operator classes."""
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
