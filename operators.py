# scripts/operators.py

import bpy
from .utils import popup_message, smart_merge_duplicate_materials, remove_unused_material_slots_manual
import logging

class MATERIALCLEANERPRO_OT_cleanse_all(bpy.types.Operator):
    """Performs all steps: Analyze, Merge, Clean"""
    bl_idname = "materialcleanerpro.cleanse_all"
    bl_label = "Cleanse All"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        props = context.scene.material_cleanerpro_props
        logging.info("Starting 'Cleanse All' operation.")

        # Step 1: Analyze
        num_mats = len(bpy.data.materials)
        props.analysis_message = f"Found {num_mats} materials."
        props.analysis_done = True
        logging.info(f"Analysis: {props.analysis_message}")

        # Step 2: Merge Duplicates
        merged = smart_merge_duplicate_materials(props)
        props.merges_message = f"Merged {merged} duplicate material(s)."
        props.merges_done = True
        logging.info(f"Merges: {props.merges_message}")

        # Step 3: Clean Unused Slots
        cleaned = remove_unused_material_slots_manual(props)
        props.cleaning_message = f"Removed {cleaned} unused material slot(s)."
        props.cleaning_done = True
        logging.info(f"Cleaning: {props.cleaning_message}")

        # Feedback
        popup_message(
            context,
            "Cleanse All Completed",
            f"Analysis: {props.analysis_message}\n"
            f"Merges: {props.merges_message}\n"
            f"Cleaning: {props.cleaning_message}",
            'INFO'
        )

        self.report({'INFO'}, "Cleanse All Completed.")
        logging.info("'Cleanse All' operation completed successfully.")
        return {'FINISHED'}

class MATERIALCLEANERPRO_OT_step_analyze(bpy.types.Operator):
    """Step 1: Analyze Materials"""
    bl_idname = "materialcleanerpro.step_analyze"
    bl_label = "1. Analyze Materials"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        props = context.scene.material_cleanerpro_props
        num_mats = len(bpy.data.materials)
        props.analysis_message = f"Found {num_mats} materials."
        props.analysis_done = True
        logging.info(f"Step 1 - Analyze: {props.analysis_message}")

        self.report({'INFO'}, "Analysis Completed.")
        return {'FINISHED'}

class MATERIALCLEANERPRO_OT_step_merge(bpy.types.Operator):
    """Step 2: Merge Duplicate Materials"""
    bl_idname = "materialcleanerpro.step_merge"
    bl_label = "2. Merge Duplicates"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        props = context.scene.material_cleanerpro_props
        if not props.analysis_done:
            self.report({'WARNING'}, "Please complete Step 1: Analyze first.")
            logging.warning("Attempted to merge duplicates without completing analysis.")
            return {'CANCELLED'}

        merged = smart_merge_duplicate_materials(props)
        props.merges_message = f"Merged {merged} duplicate material(s)."
        props.merges_done = True
        logging.info(f"Step 2 - Merge: {props.merges_message}")

        self.report({'INFO'}, f"Merged {merged} duplicate material(s).")
        return {'FINISHED'}

class MATERIALCLEANERPRO_OT_step_clean(bpy.types.Operator):
    """Step 3: Clean Unused Material Slots"""
    bl_idname = "materialcleanerpro.step_clean"
    bl_label = "3. Clean Unused Slots"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        props = context.scene.material_cleanerpro_props
        if not props.merges_done:
            self.report({'WARNING'}, "Please complete Step 2: Merge first.")
            logging.warning("Attempted to clean unused slots without completing merges.")
            return {'CANCELLED'}

        cleaned = remove_unused_material_slots_manual(props)
        props.cleaning_message = f"Removed {cleaned} unused material slot(s)."
        props.cleaning_done = True
        logging.info(f"Step 3 - Clean: {props.cleaning_message}")

        self.report({'INFO'}, f"Removed {cleaned} unused material slot(s).")
        return {'FINISHED'}

class MATERIALCLEANERPRO_OT_done(bpy.types.Operator):
    """Final Confirmation - Marks the process as done"""
    bl_idname = "materialcleanerpro.done"
    bl_label = "Done"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        props = context.scene.material_cleanerpro_props

        # Reset all steps for future runs
        props.analysis_done = False
        props.analysis_message = ""
        props.merges_done = False
        props.merges_message = ""
        props.cleaning_done = False
        props.cleaning_message = ""
        props.current_index = 0
        props.duplicates_removed_total = 0

        self.report({'INFO'}, "Process completed and reset.")
        logging.info("Process completed and reset.")

        return {'FINISHED'}
