# Test script for Material Cleaner Pro
# This script can be run in Blender's scripting workspace to test the add-on

import bpy
import sys
import os

# Add the scripts directory to the path
script_dir = os.path.dirname(os.path.abspath(__file__))
if script_dir not in sys.path:
    sys.path.append(script_dir)

def create_test_materials():
    """Create test materials for validation."""
    print("Creating test materials...")
    
    # Clear existing materials
    bpy.data.materials.clear()
    
    # Create base material
    base_mat = bpy.data.materials.new(name="TestMaterial")
    base_mat.use_nodes = True
    
    # Add some nodes
    nodes = base_mat.node_tree.nodes
    nodes.clear()
    
    # Add Principled BSDF
    principled = nodes.new(type='ShaderNodeBsdfPrincipled')
    principled.location = (0, 0)
    principled.inputs['Base Color'].default_value = (0.8, 0.2, 0.2, 1.0)
    principled.inputs['Metallic'].default_value = 0.5
    
    # Add Material Output
    output = nodes.new(type='ShaderNodeOutputMaterial')
    output.location = (300, 0)
    
    # Connect nodes
    base_mat.node_tree.links.new(principled.outputs['BSDF'], output.inputs['Surface'])
    
    # Create duplicates with same setup
    for i in range(3):
        dup_mat = base_mat.copy()
        dup_mat.name = f"TestMaterial.{i+1:03d}"
    
    # Create a different material
    diff_mat = bpy.data.materials.new(name="DifferentMaterial")
    diff_mat.use_nodes = True
    diff_nodes = diff_mat.node_tree.nodes
    diff_nodes.clear()
    
    principled2 = diff_nodes.new(type='ShaderNodeBsdfPrincipled')
    principled2.inputs['Base Color'].default_value = (0.2, 0.8, 0.2, 1.0)  # Different color
    
    output2 = diff_nodes.new(type='ShaderNodeOutputMaterial')
    diff_mat.node_tree.links.new(principled2.outputs['BSDF'], output2.inputs['Surface'])
    
    # Create orphaned material (no users)
    orphan_mat = bpy.data.materials.new(name="OrphanMaterial")
    
    print(f"Created {len(bpy.data.materials)} test materials")
    return len(bpy.data.materials)

def create_test_objects():
    """Create test objects with materials."""
    print("Creating test objects...")
    
    # Clear existing objects
    bpy.ops.object.select_all(action='SELECT')
    bpy.ops.object.delete(use_global=False)
    
    # Create cube with materials
    bpy.ops.mesh.primitive_cube_add()
    cube = bpy.context.active_object
    cube.name = "TestCube"
    
    # Assign materials to cube
    materials = list(bpy.data.materials)
    for mat in materials[:3]:  # Assign first 3 materials
        cube.data.materials.append(mat)
    
    # Add empty material slot
    cube.data.materials.append(None)
    
    print(f"Created test objects with {len(cube.data.materials)} material slots")

def test_material_functions():
    """Test the material cleaning functions."""
    print("\n" + "="*50)
    print("TESTING MATERIAL CLEANER PRO FUNCTIONS")
    print("="*50)
    
    try:
        from utils import (
            get_base_name, 
            material_node_signature, 
            smart_merge_duplicate_materials,
            remove_unused_material_slots_manual,
            validate_material_integrity,
            cleanup_orphaned_materials,
            get_memory_usage_info
        )
        from properties import MaterialCleanerProProps
        
        # Register properties for testing
        bpy.utils.register_class(MaterialCleanerProProps)
        bpy.types.Scene.test_props = bpy.props.PointerProperty(type=MaterialCleanerProProps)
        props = bpy.context.scene.test_props
        
        print("\n1. Testing get_base_name function:")
        test_names = ["Material.001", "TestMat.123", "SimpleMat", "Complex.Name.456"]
        for name in test_names:
            base = get_base_name(name)
            print(f"  '{name}' -> '{base}'")
        
        print("\n2. Testing material_node_signature function:")
        for mat in bpy.data.materials:
            try:
                sig = material_node_signature(mat)
                print(f"  Material '{mat.name}': {type(sig)} signature")
            except Exception as e:
                print(f"  ERROR with material '{mat.name}': {e}")
        
        print("\n3. Testing smart_merge_duplicate_materials:")
        initial_count = len(bpy.data.materials)
        merged = smart_merge_duplicate_materials(props)
        final_count = len(bpy.data.materials)
        print(f"  Initial materials: {initial_count}")
        print(f"  Merged duplicates: {merged}")
        print(f"  Final materials: {final_count}")
        
        print("\n4. Testing remove_unused_material_slots_manual:")
        removed_slots = remove_unused_material_slots_manual(props)
        print(f"  Removed unused slots: {removed_slots}")
        
        print("\n5. Testing validate_material_integrity:")
        issues = validate_material_integrity()
        print(f"  Issues found: {len(issues)}")
        for issue in issues:
            print(f"    - {issue}")
        
        print("\n6. Testing cleanup_orphaned_materials:")
        orphaned = cleanup_orphaned_materials()
        print(f"  Orphaned materials removed: {orphaned}")
        
        print("\n7. Testing get_memory_usage_info:")
        memory_info = get_memory_usage_info()
        for key, value in memory_info.items():
            print(f"  {key}: {value}")
        
        # Cleanup
        del bpy.types.Scene.test_props
        bpy.utils.unregister_class(MaterialCleanerProProps)
        
        print("\n" + "="*50)
        print("ALL TESTS COMPLETED SUCCESSFULLY!")
        print("="*50)
        
        return True
        
    except Exception as e:
        print(f"\nTEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_full_test():
    """Run the complete test suite."""
    print("Starting Material Cleaner Pro Test Suite...")
    
    try:
        # Create test data
        create_test_materials()
        create_test_objects()
        
        # Run function tests
        success = test_material_functions()
        
        if success:
            print("\n✅ All tests passed!")
        else:
            print("\n❌ Some tests failed!")
            
        return success
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    run_full_test()
