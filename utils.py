# scripts/utils.py

import bpy
import re
import logging
import os

# Configure logging
# Define the log file path within Blender's temporary directory
log_file_path = os.path.join(bpy.utils.user_resource('TEMP'), "material_cleaner_pro.log")

# Ensure the log directory exists
os.makedirs(os.path.dirname(log_file_path), exist_ok=True)

logging.basicConfig(
    filename=log_file_path,
    filemode='w',  # Overwrite the log file each time
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def safe_remove_draw_handler(handle):
    """
    Safely removes a draw handler if it exists.
    """
    if handle is not None:
        try:
            bpy.types.SpaceView3D.draw_handler_remove(handle, 'WINDOW')
            logging.debug("Successfully removed draw handler.")
        except ValueError:
            logging.warning("Attempted to remove a draw handler that doesn't exist or is already removed.")

def popup_message(context, title, message, icon='INFO'):
    """Displays a small popup dialog."""
    def draw(self, _context):
        for line in message.split("\n"):
            self.layout.label(text=line)
    context.window_manager.popup_menu(draw, title=title, icon=icon)
    logging.debug(f"Popup message displayed: {title} - {message}")

def get_base_name(mat_name):
    """
    Returns the base name by removing trailing .001, .002, etc.

    Args:
        mat_name (str): The material name to process

    Returns:
        str: The base name without numeric suffix
    """
    if not isinstance(mat_name, str):
        logging.warning(f"Invalid material name type: {type(mat_name)}")
        return str(mat_name)

    if not mat_name.strip():
        logging.warning("Empty material name provided")
        return "Unnamed_Material"

    base = re.sub(r"\.\d+$", "", mat_name.strip())
    logging.debug(f"Original material name: '{mat_name}' | Base name: '{base}'")
    return base

def material_node_signature(mat):
    """
    Generates a signature of the material's node setup for comparison.
    Handles sockets without a default_value attribute gracefully.

    Args:
        mat: Blender material object

    Returns:
        tuple: Signature representing the material's node setup

    Raises:
        ValueError: If material is invalid
    """
    if not mat:
        raise ValueError("Material is None")

    if not hasattr(mat, 'name'):
        raise ValueError("Invalid material object")

    try:
        if not mat.use_nodes or not mat.node_tree:
            logging.debug(f"Material '{mat.name}' does not use nodes or has no node tree.")
            return "NO_NODES"

        node_items = []

        # Sort nodes by name for consistent ordering
        try:
            sorted_nodes = sorted(mat.node_tree.nodes, key=lambda n: n.name)
        except Exception as e:
            logging.warning(f"Error sorting nodes for material '{mat.name}': {e}")
            sorted_nodes = list(mat.node_tree.nodes)

        for node in sorted_nodes:
            try:
                node_repr = [node.bl_idname, node.name]
                logging.debug(f"Processing node: {node.name} (Type: {node.bl_idname})")

                # Sort inputs by name for consistent ordering
                try:
                    sorted_inputs = sorted(node.inputs, key=lambda i: i.name)
                except Exception as e:
                    logging.warning(f"Error sorting inputs for node '{node.name}': {e}")
                    sorted_inputs = list(node.inputs)

                for inp in sorted_inputs:
                    try:
                        if inp.is_linked:
                            node_repr.append(f"{inp.name}:LINKED")
                            logging.debug(f"  Socket '{inp.name}' is linked.")
                        else:
                            if hasattr(inp, "default_value"):
                                dv = inp.default_value
                                try:
                                    if isinstance(dv, float):
                                        # Handle NaN and infinity values
                                        if not (dv == dv):  # NaN check
                                            node_repr.append(f"{inp.name}:NaN")
                                        elif dv == float('inf'):
                                            node_repr.append(f"{inp.name}:INF")
                                        elif dv == float('-inf'):
                                            node_repr.append(f"{inp.name}:-INF")
                                        else:
                                            node_repr.append(f"{inp.name}:{dv:.4f}")
                                        logging.debug(f"  Socket '{inp.name}' has default float value: {dv:.4f}")
                                    elif hasattr(dv, "__len__") and not isinstance(dv, str):
                                        try:
                                            dv_str = ",".join(f"{v:.4f}" if isinstance(v, (int, float)) and v == v else "NaN" for v in dv)
                                            node_repr.append(f"{inp.name}:[{dv_str}]")
                                            logging.debug(f"  Socket '{inp.name}' has default vector/color value: [{dv_str}]")
                                        except (TypeError, ValueError) as e:
                                            node_repr.append(f"{inp.name}:INVALID_VECTOR")
                                            logging.warning(f"  Socket '{inp.name}' has invalid vector value: {e}")
                                    else:
                                        node_repr.append(f"{inp.name}:{str(dv)[:50]}")  # Limit string length
                                        logging.debug(f"  Socket '{inp.name}' has default value: {dv}")
                                except Exception as e:
                                    node_repr.append(f"{inp.name}:ERROR")
                                    logging.warning(f"  Error processing socket '{inp.name}' default value: {e}")
                            else:
                                # For sockets without default_value (e.g., shader sockets)
                                node_repr.append(f"{inp.name}:NO_DEFAULT")
                                logging.debug(f"  Socket '{inp.name}' has no default value.")
                    except Exception as e:
                        logging.warning(f"Error processing input '{inp.name}' for node '{node.name}': {e}")
                        node_repr.append(f"{inp.name}:ERROR")

                node_items.append(tuple(node_repr))

            except Exception as e:
                logging.warning(f"Error processing node '{node.name}' in material '{mat.name}': {e}")
                # Add a placeholder for the problematic node
                node_items.append(("ERROR_NODE", str(e)[:50]))

        signature = tuple(node_items)
        logging.debug(f"Signature for material '{mat.name}': {len(signature)} nodes processed")
        return signature

    except Exception as e:
        logging.error(f"Critical error generating signature for material '{mat.name}': {e}")
        return f"ERROR_{hash(mat.name) % 10000}"  # Fallback signature

def replace_material(bad_mat, good_mat):
    """
    Remaps all users of bad_mat to good_mat with comprehensive error handling.

    Args:
        bad_mat: Material to be replaced
        good_mat: Material to replace with

    Returns:
        bool: True if replacement was successful, False otherwise
    """
    if not bad_mat or not good_mat:
        logging.error("Invalid materials provided for replacement")
        return False

    if bad_mat == good_mat:
        logging.warning(f"Attempted to replace material '{bad_mat.name}' with itself")
        return False

    try:
        # Check if materials still exist in the data
        if bad_mat.name not in bpy.data.materials:
            logging.warning(f"Source material '{bad_mat.name}' no longer exists")
            return False

        if good_mat.name not in bpy.data.materials:
            logging.warning(f"Target material '{good_mat.name}' no longer exists")
            return False

        # Perform the replacement
        bad_mat.user_remap(good_mat)
        logging.debug(f"Successfully replaced users of material '{bad_mat.name}' with '{good_mat.name}'.")
        return True

    except ReferenceError as e:
        logging.warning(f"Reference error remapping material '{bad_mat.name}': {e}")
        return False
    except AttributeError as e:
        logging.error(f"Attribute error remapping material '{bad_mat.name}': {e}")
        return False
    except Exception as e:
        logging.error(f"Unexpected error remapping material '{bad_mat.name}': {e}")
        return False

def smart_merge_duplicate_materials(props, use_chunking=False):
    """
    Merges duplicate materials that share the same base name and have identical node setups.
    Returns the number of duplicates merged.

    Args:
        props: Property group containing settings and progress tracking
        use_chunking: Whether to use chunk processing for large material counts
    """
    mats = bpy.data.materials
    if not mats:
        logging.info("No materials found to merge.")
        return 0

    # Initialize progress tracking
    if use_chunking:
        props.progress_current = 0
        props.progress_total = len(mats)
        props.progress_message = "Analyzing materials..."

    # Group materials by base name
    groups = {}
    for i, mat in enumerate(mats):
        if use_chunking and i % props.chunk_size == 0:
            props.progress_current = i
            props.progress_message = f"Grouping materials... ({i}/{len(mats)})"
            # Force UI update
            bpy.context.view_layer.update()

        base = get_base_name(mat.name)
        groups.setdefault(base, []).append(mat)

    total_merged = 0
    group_count = 0
    total_groups = len([g for g in groups.values() if len(g) >= 2])

    # Iterate through each group
    for base_name, materials in groups.items():
        if len(materials) < 2:
            continue  # No duplicates in this group

        group_count += 1
        if use_chunking:
            props.progress_message = f"Processing group {group_count}/{total_groups}: '{base_name}'"

        logging.debug(f"Processing group '{base_name}' with {len(materials)} materials.")

        # Further group by node signature
        sig_map = {}
        for mat in materials:
            try:
                sig = material_node_signature(mat)
                sig_map.setdefault(sig, []).append(mat)
            except Exception as e:
                logging.warning(f"Error processing material '{mat.name}': {e}")
                continue

        # Merge duplicates within each signature group
        for sig, mat_list in sig_map.items():
            if len(mat_list) < 2:
                continue  # No duplicates in this signature group

            master_mat = mat_list[0]
            duplicates = mat_list[1:]

            logging.debug(f"Merging {len(duplicates)} duplicates into '{master_mat.name}'.")

            for dup in duplicates:
                try:
                    # Check if material still exists (might have been removed already)
                    if dup.name in bpy.data.materials:
                        replace_material(dup, master_mat)
                        if dup.users == 0:
                            bpy.data.materials.remove(dup)
                            total_merged += 1
                            logging.info(f"Removed duplicate material '{dup.name}'.")
                except Exception as e:
                    logging.error(f"Error removing duplicate material '{dup.name}': {e}")
                    continue

            # Optionally remove trailing .### from master
            try:
                if re.search(r"\.\d+$", master_mat.name):
                    new_name = get_base_name(master_mat.name)
                    # Check if the new name already exists
                    if new_name not in bpy.data.materials:
                        logging.debug(f"Renaming master material from '{master_mat.name}' to '{new_name}'.")
                        master_mat.name = new_name
            except Exception as e:
                logging.warning(f"Error renaming master material '{master_mat.name}': {e}")

    # Clear progress tracking
    if use_chunking:
        props.progress_current = 0
        props.progress_total = 0
        props.progress_message = ""

    logging.info(f"Total duplicates merged: {total_merged}")
    return total_merged

def remove_unused_material_slots_manual(props, use_chunking=False, selected_only=False):
    """
    Manually removes unused material slots by popping them from obj.data.materials.

    Args:
        props: Property group containing settings and progress tracking
        use_chunking: Whether to use chunk processing for large object counts
        selected_only: Whether to process only selected objects
    """
    total_slots_removed = 0

    # Get objects to process
    if selected_only:
        objects = [obj for obj in bpy.context.selected_objects
                  if hasattr(obj, "data") and hasattr(obj.data, "materials")]
    else:
        objects = [obj for obj in bpy.data.objects
                  if hasattr(obj, "data") and hasattr(obj.data, "materials")]

    if not objects:
        logging.info("No objects with materials found to process.")
        return 0

    # Initialize progress tracking
    if use_chunking:
        props.progress_current = 0
        props.progress_total = len(objects)
        props.progress_message = "Cleaning material slots..."

    for i, obj in enumerate(objects):
        if use_chunking and i % props.chunk_size == 0:
            props.progress_current = i
            props.progress_message = f"Processing object {i+1}/{len(objects)}: '{obj.name}'"
            # Force UI update
            bpy.context.view_layer.update()

        try:
            material_list = obj.data.materials
            slots_removed_this_obj = 0

            # Iterate in reverse to safely remove slots
            for slot_idx in reversed(range(len(material_list))):
                mat = material_list[slot_idx]
                if mat is None or mat.users == 0:
                    material_list.pop(index=slot_idx)
                    slots_removed_this_obj += 1
                    total_slots_removed += 1
                    logging.debug(f"Removed unused material slot at index {slot_idx} from object '{obj.name}'.")

            if slots_removed_this_obj > 0:
                logging.info(f"Removed {slots_removed_this_obj} unused material slots from object '{obj.name}'.")

        except Exception as e:
            logging.error(f"Error processing object '{obj.name}': {e}")
            continue

    # Clear progress tracking
    if use_chunking:
        props.progress_current = 0
        props.progress_total = 0
        props.progress_message = ""

    logging.info(f"Total unused material slots removed: {total_slots_removed}")
    return total_slots_removed


def validate_material_integrity():
    """
    Validates the integrity of materials in the scene.
    Returns a report of any issues found.
    """
    issues = []

    # Check for materials with broken node trees
    for mat in bpy.data.materials:
        if mat.use_nodes and mat.node_tree:
            try:
                # Try to access node tree properties
                nodes = mat.node_tree.nodes
                for node in nodes:
                    # Basic validation - try to access node properties
                    _ = node.name, node.bl_idname
            except Exception as e:
                issues.append(f"Material '{mat.name}' has corrupted node tree: {e}")

    # Check for orphaned materials
    orphaned_materials = [mat for mat in bpy.data.materials if mat.users == 0]
    if orphaned_materials:
        issues.append(f"Found {len(orphaned_materials)} orphaned materials with no users")

    return issues


def cleanup_orphaned_materials():
    """
    Removes materials that have no users.
    Returns the number of materials removed.
    """
    removed_count = 0
    orphaned_materials = [mat for mat in bpy.data.materials if mat.users == 0]

    for mat in orphaned_materials:
        try:
            bpy.data.materials.remove(mat)
            removed_count += 1
            logging.info(f"Removed orphaned material '{mat.name}'.")
        except Exception as e:
            logging.error(f"Error removing orphaned material '{mat.name}': {e}")

    return removed_count


def optimize_material_performance():
    """
    Optimizes material performance by identifying and fixing common issues.

    Returns:
        dict: Report of optimizations performed
    """
    report = {
        'simplified_nodes': 0,
        'removed_unused_nodes': 0,
        'optimized_textures': 0,
        'issues_found': []
    }

    for mat in bpy.data.materials:
        if not mat.use_nodes or not mat.node_tree:
            continue

        try:
            # Remove unused nodes
            unused_nodes = []
            for node in mat.node_tree.nodes:
                # Check if node has any connected outputs
                has_connected_outputs = any(output.is_linked for output in node.outputs)

                # Skip output nodes and nodes with connected outputs
                if node.bl_idname in ['ShaderNodeOutputMaterial', 'ShaderNodeOutputWorld'] or has_connected_outputs:
                    continue

                # Check if it's a dead-end node
                has_connected_inputs = any(inp.is_linked for inp in node.inputs)
                if not has_connected_inputs:
                    unused_nodes.append(node)

            # Remove unused nodes
            for node in unused_nodes:
                mat.node_tree.nodes.remove(node)
                report['removed_unused_nodes'] += 1
                logging.debug(f"Removed unused node '{node.name}' from material '{mat.name}'")

        except Exception as e:
            report['issues_found'].append(f"Error optimizing material '{mat.name}': {e}")
            logging.error(f"Error optimizing material '{mat.name}': {e}")

    return report


def get_memory_usage_info():
    """
    Gets information about memory usage of materials and textures.

    Returns:
        dict: Memory usage information
    """
    info = {
        'total_materials': len(bpy.data.materials),
        'materials_with_nodes': 0,
        'total_textures': len(bpy.data.textures),
        'total_images': len(bpy.data.images),
        'estimated_memory_mb': 0
    }

    # Count materials with nodes
    for mat in bpy.data.materials:
        if mat.use_nodes and mat.node_tree:
            info['materials_with_nodes'] += 1

    # Estimate memory usage from images
    for img in bpy.data.images:
        if img.size[0] > 0 and img.size[1] > 0:
            # Rough estimation: width * height * channels * bytes_per_channel
            channels = 4 if img.alpha_mode != 'NONE' else 3
            bytes_per_pixel = channels * (2 if img.is_float else 1)  # Simplified
            memory_bytes = img.size[0] * img.size[1] * bytes_per_pixel
            info['estimated_memory_mb'] += memory_bytes / (1024 * 1024)

    return info


def batch_rename_materials(pattern, replacement):
    """
    Batch rename materials using regex pattern.

    Args:
        pattern (str): Regex pattern to match
        replacement (str): Replacement string

    Returns:
        int: Number of materials renamed
    """
    if not pattern or not isinstance(pattern, str):
        logging.error("Invalid pattern provided for batch rename")
        return 0

    renamed_count = 0

    try:
        compiled_pattern = re.compile(pattern)
    except re.error as e:
        logging.error(f"Invalid regex pattern '{pattern}': {e}")
        return 0

    for mat in bpy.data.materials:
        try:
            old_name = mat.name
            new_name = compiled_pattern.sub(replacement, old_name)

            if new_name != old_name and new_name.strip():
                # Check if new name already exists
                if new_name not in bpy.data.materials:
                    mat.name = new_name
                    renamed_count += 1
                    logging.info(f"Renamed material '{old_name}' to '{new_name}'")
                else:
                    logging.warning(f"Cannot rename '{old_name}' to '{new_name}': name already exists")

        except Exception as e:
            logging.error(f"Error renaming material '{mat.name}': {e}")

    return renamed_count
