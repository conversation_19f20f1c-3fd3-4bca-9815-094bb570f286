# scripts/utils.py

import bpy
import re
import logging
import os

# Configure logging
# Define the log file path within Blender's temporary directory
log_file_path = os.path.join(bpy.utils.user_resource('TEMP'), "material_cleaner_pro.log")

# Ensure the log directory exists
os.makedirs(os.path.dirname(log_file_path), exist_ok=True)

logging.basicConfig(
    filename=log_file_path,
    filemode='w',  # Overwrite the log file each time
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def safe_remove_draw_handler(handle):
    """
    Safely removes a draw handler if it exists.
    """
    if handle is not None:
        try:
            bpy.types.SpaceView3D.draw_handler_remove(handle, 'WINDOW')
            logging.debug("Successfully removed draw handler.")
        except ValueError:
            logging.warning("Attempted to remove a draw handler that doesn't exist or is already removed.")

def popup_message(context, title, message, icon='INFO'):
    """Displays a small popup dialog."""
    def draw(self, _context):
        for line in message.split("\n"):
            self.layout.label(text=line)
    context.window_manager.popup_menu(draw, title=title, icon=icon)
    logging.debug(f"Popup message displayed: {title} - {message}")

def get_base_name(mat_name):
    """Returns the base name by removing trailing .001, .002, etc."""
    base = re.sub(r"\.\d+$", "", mat_name)
    logging.debug(f"Original material name: '{mat_name}' | Base name: '{base}'")
    return base

def material_node_signature(mat):
    """
    Generates a signature of the material's node setup for comparison.
    Handles sockets without a default_value attribute gracefully.
    """
    if not mat.use_nodes or not mat.node_tree:
        logging.debug(f"Material '{mat.name}' does not use nodes or has no node tree.")
        return "NO_NODES"

    node_items = []
    for node in sorted(mat.node_tree.nodes, key=lambda n: n.name):
        node_repr = [node.bl_idname, node.name]
        logging.debug(f"Processing node: {node.name} (Type: {node.bl_idname})")

        for inp in sorted(node.inputs, key=lambda i: i.name):
            if inp.is_linked:
                node_repr.append(f"{inp.name}:LINKED")
                logging.debug(f"  Socket '{inp.name}' is linked.")
            else:
                if hasattr(inp, "default_value"):
                    dv = inp.default_value
                    if isinstance(dv, float):
                        node_repr.append(f"{inp.name}:{dv:.4f}")
                        logging.debug(f"  Socket '{inp.name}' has default float value: {dv:.4f}")
                    elif hasattr(dv, "__len__") and not isinstance(dv, str):
                        dv_str = ",".join(f"{v:.4f}" for v in dv)
                        node_repr.append(f"{inp.name}:[{dv_str}]")
                        logging.debug(f"  Socket '{inp.name}' has default vector/color value: [{dv_str}]")
                    else:
                        node_repr.append(f"{inp.name}:{dv}")
                        logging.debug(f"  Socket '{inp.name}' has default value: {dv}")
                else:
                    # For sockets without default_value (e.g., shader sockets)
                    node_repr.append(f"{inp.name}:NO_DEFAULT")
                    logging.debug(f"  Socket '{inp.name}' has no default value.")

        node_items.append(tuple(node_repr))

    signature = tuple(node_items)
    logging.debug(f"Signature for material '{mat.name}': {signature}")
    return signature

def replace_material(bad_mat, good_mat):
    """Remaps all users of bad_mat to good_mat."""
    try:
        bad_mat.user_remap(good_mat)
        logging.debug(f"Replaced users of material '{bad_mat.name}' with '{good_mat.name}'.")
    except ReferenceError:
        logging.warning(f"Could not remap material '{bad_mat.name}' (possibly already removed).")

def smart_merge_duplicate_materials(props):
    """
    Merges duplicate materials that share the same base name and have identical node setups.
    Returns the number of duplicates merged.
    """
    mats = bpy.data.materials
    if not mats:
        logging.info("No materials found to merge.")
        return 0

    # Group materials by base name
    groups = {}
    for mat in mats:
        base = get_base_name(mat.name)
        groups.setdefault(base, []).append(mat)

    total_merged = 0

    # Iterate through each group
    for base_name, materials in groups.items():
        if len(materials) < 2:
            continue  # No duplicates in this group

        logging.debug(f"Processing group '{base_name}' with {len(materials)} materials.")

        # Further group by node signature
        sig_map = {}
        for mat in materials:
            sig = material_node_signature(mat)
            sig_map.setdefault(sig, []).append(mat)

        # Merge duplicates within each signature group
        for sig, mat_list in sig_map.items():
            if len(mat_list) < 2:
                continue  # No duplicates in this signature group

            master_mat = mat_list[0]
            duplicates = mat_list[1:]

            logging.debug(f"Merging {len(duplicates)} duplicates into '{master_mat.name}'.")

            for dup in duplicates:
                # **FIXED LINE:** Check by material name instead of object
                if dup.name in bpy.data.materials:
                    replace_material(dup, master_mat)
                    if dup.users == 0:
                        bpy.data.materials.remove(dup)
                        total_merged += 1
                        logging.info(f"Removed duplicate material '{dup.name}'.")

            # Optionally remove trailing .### from master
            if re.search(r"\.\d+$", master_mat.name):
                new_name = get_base_name(master_mat.name)
                logging.debug(f"Renaming master material from '{master_mat.name}' to '{new_name}'.")
                master_mat.name = new_name

    logging.info(f"Total duplicates merged: {total_merged}")
    return total_merged

def remove_unused_material_slots_manual(props):
    """
    Manually removes unused material slots by popping them from obj.data.materials.
    """
    total_slots_removed = 0
    objects = list(bpy.data.objects)

    for obj in objects:
        if not hasattr(obj, "data") or not hasattr(obj.data, "materials"):
            continue

        material_list = obj.data.materials

        # Iterate in reverse to safely remove slots
        for slot_idx in reversed(range(len(material_list))):
            mat = material_list[slot_idx]
            if mat is None or mat.users == 0:
                material_list.pop(index=slot_idx)
                total_slots_removed += 1
                logging.debug(f"Removed unused material slot at index {slot_idx} from object '{obj.name}'.")

    logging.info(f"Total unused material slots removed: {total_slots_removed}")
    return total_slots_removed
