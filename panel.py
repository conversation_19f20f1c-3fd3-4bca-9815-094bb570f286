# scripts/panel.py

import bpy

class MATERIALCLEANERPRO_PT_panel(bpy.types.Panel):
    """Creates a panel in the 3D View N-Panel for Material Cleaner Pro"""
    bl_label = "Material Cleaner Pro"
    bl_idname = "MATERIALCLEANERPRO_PT_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "Material Cleaner Pro"

    def draw(self, context):
        layout = self.layout
        props = context.scene.material_cleanerpro_props

        # Single Button: Cleanse All
        row = layout.row()
        row.operator("materialcleanerpro.cleanse_all", text="Cleanse All")
        layout.separator()

        # Step-by-Step
        layout.label(text="Step-by-Step Cleaning:", icon='INFO')

        # Step 1: Analyze
        row = layout.row()
        row.enabled = not props.analysis_done
        row.operator("materialcleanerpro.step_analyze", text="1. Analyze Materials")
        if props.analysis_done:
            layout.label(text=f"Analysis: {props.analysis_message}", icon='CHECKMARK')

        # Step 2: Merge
        row = layout.row()
        row.enabled = props.analysis_done and not props.merges_done
        row.operator("materialcleanerpro.step_merge", text="2. Merge Duplicates")
        if props.merges_done:
            layout.label(text=f"Merges: {props.merges_message}", icon='CHECKMARK')

        # Step 3: Clean
        row = layout.row()
        row.enabled = props.merges_done and not props.cleaning_done
        row.operator("materialcleanerpro.step_clean", text="3. Clean Unused Slots")
        if props.cleaning_done:
            layout.label(text=f"Cleaning: {props.cleaning_message}", icon='CHECKMARK')

        layout.separator()

        # Final Done Button
        row = layout.row()
        row.enabled = props.analysis_done and props.merges_done and props.cleaning_done
        row.operator("materialcleanerpro.done", text="Done")

        # Advanced Operations
        layout.separator()
        layout.label(text="Advanced Operations:", icon='TOOL_SETTINGS')

        # Create a sub-layout for advanced operations
        box = layout.box()
        box.operator("materialcleanerpro.validate_materials", text="Validate Materials", icon='CHECKMARK')
        box.operator("materialcleanerpro.chunk_process", text="Chunk Process (Large Projects)", icon='MOD_ARRAY')

        # Object-Level Operations
        layout.separator()
        layout.label(text="Object-Level Operations:", icon='OBJECT_DATA')
        layout.operator("materialcleanerpro.remove_all_materials", text="Remove All Materials")
        layout.operator("materialcleanerpro.clean_materials", text="Clean Materials")

        # Progress indicator (if processing)
        if props.progress_total > 0:
            layout.separator()
            layout.label(text="Processing...", icon='TIME')

            # Progress message
            if props.progress_message:
                layout.label(text=props.progress_message, icon='INFO')

            # Progress bar
            row = layout.row()
            row.scale_y = 1.2
            progress_factor = props.progress_current / props.progress_total if props.progress_total > 0 else 0
            row.progress(factor=progress_factor, text=f"{props.progress_current}/{props.progress_total} ({progress_factor*100:.0f}%)")

            # Cancel button (if needed)
            # row = layout.row()
            # row.operator("materialcleanerpro.cancel_process", text="Cancel", icon='CANCEL')

        # Statistics
        layout.separator()
        layout.label(text="Statistics:", icon='INFO')

        # Material count
        num_materials = len(bpy.data.materials)
        layout.label(text=f"Total Materials: {num_materials}")

        # Object count with materials
        num_objects_with_mats = len([obj for obj in bpy.data.objects
                                   if hasattr(obj, "data") and hasattr(obj.data, "materials") and obj.data.materials])
        layout.label(text=f"Objects with Materials: {num_objects_with_mats}")

        # Settings
        layout.separator()
        layout.label(text="Settings:", icon='PREFERENCES')
        layout.prop(props, "chunk_size", text="Chunk Size")

        # Help text
        layout.separator()
        box = layout.box()
        box.label(text="Tips:", icon='QUESTION')
        box.label(text="• Use 'Cleanse All' for quick cleanup")
        box.label(text="• Use step-by-step for control")
        box.label(text="• Use chunk processing for 500+ materials")
        box.label(text="• Validate materials to check for issues")


# ----------------------------------------------------------------
# Registration
# ----------------------------------------------------------------
classes = (
    MATERIALCLEANERPRO_PT_panel,
)


def register():
    """Register all panel classes."""
    for cls in classes:
        bpy.utils.register_class(cls)


def unregister():
    """Unregister all panel classes."""
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
