# scripts/panel.py

import bpy

class MATERIALCLEANERPRO_PT_panel(bpy.types.Panel):
    """Creates a panel in the 3D View N-Panel for Material Cleaner Pro"""
    bl_label = "Material Cleaner Pro"
    bl_idname = "MATERIALCLEANERPRO_PT_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "Material Cleaner Pro"

    def draw(self, context):
        layout = self.layout
        props = context.scene.material_cleanerpro_props

        # Single Button: Cleanse All
        row = layout.row()
        row.operator("materialcleanerpro.cleanse_all", text="Cleanse All")
        layout.separator()

        # Step-by-Step
        layout.label(text="Step-by-Step Cleaning:", icon='INFO')

        # Step 1: Analyze
        row = layout.row()
        row.enabled = not props.analysis_done
        row.operator("materialcleanerpro.step_analyze", text="1. Analyze Materials")
        if props.analysis_done:
            layout.label(text=f"Analysis: {props.analysis_message}", icon='CHECKMARK')

        # Step 2: Merge
        row = layout.row()
        row.enabled = props.analysis_done and not props.merges_done
        row.operator("materialcleanerpro.step_merge", text="2. Merge Duplicates")
        if props.merges_done:
            layout.label(text=f"Merges: {props.merges_message}", icon='CHECKMARK')

        # Step 3: Clean
        row = layout.row()
        row.enabled = props.merges_done and not props.cleaning_done
        row.operator("materialcleanerpro.step_clean", text="3. Clean Unused Slots")
        if props.cleaning_done:
            layout.label(text=f"Cleaning: {props.cleaning_message}", icon='CHECKMARK')

        layout.separator()

        # Final Done Button
        row = layout.row()
        row.enabled = props.analysis_done and props.merges_done and props.cleaning_done
        row.operator("materialcleanerpro.done", text="Done")

        # Optional: Chunk Size for Stepwise (if you expand stepwise to handle chunking)
        # For now, chunking is integrated within the step operators if needed
